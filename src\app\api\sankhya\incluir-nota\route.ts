import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  try {
    const { bearerToken, codparc, itens } = await request.json();

    if (!bearerToken) {
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!codparc) {
      return NextResponse.json(
        { error: 'CODPARC é obrigatório' },
        { status: 400 }
      );
    }

    if (!itens || !Array.isArray(itens) || itens.length === 0) {
      return NextResponse.json(
        { error: 'Itens do pedido são obrigatórios' },
        { status: 400 }
      );
    }

    const SANKHYA_API_URL = process.env.SANKHYA_INCLUIR_NOTA_URL;
    const hoje = new Date();
    const dataFormatada = hoje.toLocaleDateString('pt-BR');

    const itensFormatados = itens.map((item: any) => ({
      NUNOTA: {},
      IGNOREDESCPROMOQTD: { $: "False" },
      CODPROD: { $: item.codprod.toString() },
      QTDNEG: { $: item.quantidade.toString() },
      CODLOCALORIG: { $: "101100" },
      CODVOL: { $: "CX" }
    }));

    const requestBody = {
      serviceName: "CACSP.incluirNota",
      requestBody: {
        nota: {
          cabecalho: {
            NUNOTA: {},
            CODPARC: { $: codparc.toString() },
            DTNEG: { $: dataFormatada },
            CODTIPOPER: { $: "1000" },
            CODTIPVENDA: { $: "11" },
            CODVEND: { $: "4" },
            CODEMP: { $: "3" },
            TIPMOV: { $: "P" },
            CODCENCUS: { $: "40010" },
            CODNAT: { $: "310001" },
            OBSERVACAO: { $: "PEDIDO FUNCIONARIO" }
          },
          itens: {
            INFORMARPRECO: "False",
            item: itensFormatados
          }
        }
      }
    };

    const response = await axios.post(SANKHYA_API_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao incluir nota',
        success: false
      },
      { status: 500 }
    );
  }
}
