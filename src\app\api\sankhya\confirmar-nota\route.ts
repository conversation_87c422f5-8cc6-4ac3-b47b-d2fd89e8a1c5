import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
  console.log('🔍 API Route /api/sankhya/confirmar-nota - Iniciando requisição');
  
  try {
    const { bearerToken, nunota } = await request.json();
    console.log('🔑 Token recebido:', bearerToken ? 'Token presente' : 'Token ausente');
    console.log('📄 NUNOTA recebido:', nunota);

    if (!bearerToken) {
      console.error('❌ Token de autenticação não fornecido');
      return NextResponse.json(
        { error: 'Token de autenticação é obrigatório' },
        { status: 400 }
      );
    }

    if (!nunota) {
      console.error('❌ NUNOTA não fornecido');
      return NextResponse.json(
        { error: 'NUNOTA é obrigatório' },
        { status: 400 }
      );
    }

    const SANKHYA_API_URL = process.env.SANKHYA_CONFIRMAR_NOTA_URL
    console.log('🌐 URL da API Sankhya:', SANKHYA_API_URL);

    const requestBody = {
      serviceName: "CACSP.confirmarNota",
      mgeSession: "jsessionid",
      requestBody: {
        nota: {
          NUNOTA: { $: nunota.toString() }
        }
      }
    };

    console.log('📤 Corpo da requisição:', JSON.stringify(requestBody, null, 2));
    console.log('📡 Fazendo requisição para API Sankhya...');

    const response = await axios.post(SANKHYA_API_URL, requestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Resposta da API Sankhya:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    });

    console.log('📊 Dados completos da resposta:', JSON.stringify(response.data, null, 2));

    return NextResponse.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('💥 Erro na API Route /api/sankhya/confirmar-nota:', error);
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      console.error('📡 Detalhes do erro HTTP:', {
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
        headers: axiosError.response?.headers
      });
      
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false,
          details: axiosError.response?.data
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao confirmar nota',
        success: false
      },
      { status: 500 }
    );
  }
}
