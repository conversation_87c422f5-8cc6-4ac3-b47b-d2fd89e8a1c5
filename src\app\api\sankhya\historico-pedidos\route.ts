import { NextResponse } from 'next/server';
import axios from 'axios';

interface SankhyaLoginResponse {
  bearerToken: string;
}

interface SankhyaApiResponse {
  status: string;
  statusMessage?: string;
  responseBody?: {
    entities?: {
      entity?: any | any[];
    };
  };
}

interface HistoricoPedido {
  nunota: number;
  codparc: number;
  vlrnota: number;
  itens: ItemPedido[];
}

interface ItemPedido {
  codprod: number;
  descrprod: string;
  vlrunit: number;
  qtdneg: number;
}

export async function POST(request: Request) {
  let bearerToken: string | null = null;

  try {
    const { codparc } = await request.json();

    if (!codparc) {
      return NextResponse.json(
        { error: 'CODPARC é obrigatório' },
        { status: 400 }
      );
    }

    const loginUrl = process.env.SANKHYA_LOGIN_URL;

    if (!loginUrl || !process.env.SANKHYA_USUARIO || !process.env.SANKHYA_SENHA ||
        !process.env.SANKHYA_TOKEN || !process.env.SANKHYA_APPKEY) {
      return NextResponse.json(
        { error: 'Configurações da API Sankhya não encontradas no servidor' },
        { status: 500 }
      );
    }

    const loginResponse = await axios.post<SankhyaLoginResponse>(loginUrl, {}, {
      headers: {
        'username': process.env.SANKHYA_USUARIO,
        'password': process.env.SANKHYA_SENHA,
        'token': process.env.SANKHYA_TOKEN,
        'appkey': process.env.SANKHYA_APPKEY,
        'Content-Type': 'application/json'
      }
    });

    if (loginResponse.status !== 200 || !loginResponse.data.bearerToken) {
      return NextResponse.json(
        { error: 'Falha na autenticação com a API Sankhya' },
        { status: 401 }
      );
    }

    bearerToken = loginResponse.data.bearerToken;
    const cabecalhoUrl = process.env.SANKHYA_CRUD_LOAD_URL;

    if (!cabecalhoUrl) {
      return NextResponse.json(
        { error: 'URL da API Sankhya não configurada no servidor' },
        { status: 500 }
      );
    }

    const cabecalhoRequestBody = {
      serviceName: "CRUDServiceProvider.loadRecords",
      requestBody: {
        dataSet: {
          rootEntity: "CabecalhoNota",
          includePresentationFields: "S",
          offsetPage: "0",
          criteria: {
            expression: {
              $: "(this.CODPARC = ? AND this.CODTIPOPER = 1000)"
            },
            parameter: [
              {
                $: codparc.toString(),
                type: "I"
              }
            ]
          },
          entity: {
            fieldset: {
              list: "NUNOTA,CODEMP,CODPARC,DTNEG,VLRNOTA, CODTIPOPER"
            }
          }
        }
      }
    };

    const cabecalhoResponse = await axios.post<SankhyaApiResponse>(cabecalhoUrl, cabecalhoRequestBody, {
      headers: {
        'Authorization': `Bearer ${bearerToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (cabecalhoResponse.data.status !== '1') {
      throw new Error(cabecalhoResponse.data.statusMessage || 'Erro ao buscar histórico de pedidos');
    }

    const historicoPedidos: HistoricoPedido[] = [];

    if (cabecalhoResponse.data.responseBody?.entities?.entity) {
      const entities = cabecalhoResponse.data.responseBody.entities.entity;
      const entitiesArray = Array.isArray(entities) ? entities : [entities];
      const ultimosPedidos = entitiesArray.slice(-3);

      for (const entity of ultimosPedidos) {
        const nunota = parseInt(entity.f0.$);
        const codparcPedido = parseInt(entity.f2.$);
        const vlrnota = parseFloat(entity.f4.$);

        const itensRequestBody = {
          serviceName: "CRUDServiceProvider.loadRecords",
          requestBody: {
            dataSet: {
              rootEntity: "ItemNota",
              includePresentationFields: "S",
              tryJoinedFields: "true",
              offsetPage: "0",
              criteria: {
                expression: {
                  $: "NUNOTA = ?"
                },
                parameter: [
                  {
                    $: nunota.toString(),
                    type: "I"
                  }
                ]
              },
              entity: [
                {
                  path: "",
                  fieldset: {
                    list: "CODPROD, VLRUNIT, QTDNEG"
                  }
                },
                {
                  path: "Produto",
                  fieldset: {
                    list: "CODPROD, DESCRPROD"
                  }
                }
              ]
            }
          }
        };

        const itensResponse = await axios.post<SankhyaApiResponse>(cabecalhoUrl, itensRequestBody, {
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json'
          }
        });

        const itens: ItemPedido[] = [];

        if (itensResponse.data.status === '1' && itensResponse.data.responseBody?.entities?.entity) {
          const itensEntities = itensResponse.data.responseBody.entities.entity;
          const itensArray = Array.isArray(itensEntities) ? itensEntities : [itensEntities];

          for (const itemEntity of itensArray) {
            itens.push({
              codprod: parseInt(itemEntity.f0.$),
              vlrunit: parseFloat(itemEntity.f1.$),
              qtdneg: parseFloat(itemEntity.f2.$),
              descrprod: itemEntity.f6.$ || 'Produto não encontrado'
            });
          }
        }

        historicoPedidos.push({
          nunota,
          codparc: codparcPedido,
          vlrnota,
          itens
        });
      }
    }

    const logoutUrl = process.env.SANKHYA_LOGOUT_URL;
    try {
      if (logoutUrl) {
        await axios.post(logoutUrl, {}, {
        headers: {
          'Authorization': `Bearer ${bearerToken}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (logoutError) {
      // Ignore logout errors
    }

    return NextResponse.json({
      success: true,
      pedidos: historicoPedidos
    });

  } catch (error) {
    if (bearerToken) {
      try {
        const logoutUrl = process.env.SANKHYA_LOGOUT_URL;
        await axios.post(logoutUrl, {}, {
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json'
          }
        });
      } catch (logoutError) {
        // Ignore logout errors
      }
    }

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any;
      return NextResponse.json(
        {
          error: `Erro na API Sankhya: ${axiosError.response?.status} - ${axiosError.response?.data?.message || axiosError.response?.statusText}`,
          success: false
        },
        { status: axiosError.response?.status || 500 }
      );
    }

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro desconhecido ao buscar histórico de pedidos',
        success: false
      },
      { status: 500 }
    );
  }
}
